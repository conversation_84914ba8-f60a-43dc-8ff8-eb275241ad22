apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: sequential-group-deployment
  namespace: argo
spec:
  workflowMetadata:
    labels:
      app-id: "{{workflow.parameters.app_id}}"
      task-id: "{{workflow.parameters.task_id}}"
      env-id: "{{workflow.parameters.env_id}}"
  entrypoint: main
  arguments:
    parameters:
    - name: app_id
      description: "应用ID"
    - name: env_id
      description: "环境ID"
    - name: task_id
      description: "部署任务ID"
    - name: groups_order
      description: "JSON格式的有序组ID列表，按照执行顺序排列"
    - name: image_registry
      description: "镜像仓库地址"
    - name: image_repo
      description: "镜像仓库名"
      default: "{{workflow.parameters.app_id}}"
    - name: image_user
      description: "镜像仓库用户名"
    - name: image_password
      description: "镜像仓库密码"
    - name: deploy_url
      description: "部署服务API地址"
    - name: git_username
      description: "Git用户名"
    - name: git_token
      description: "Git访问令牌"
  templates:
  - name: main
    dag:
      tasks:
      - name: generate-deployment-plan
        template: generate-deployment-plan
        arguments:
          parameters:
          - name: groups_order
            value: "{{workflow.parameters.groups_order}}"

      # 按顺序部署所有组
      - name: deploy-all-groups
        template: process-groups-sequentially
        dependencies:
        - generate-deployment-plan
        arguments:
          parameters:
          - name: groups_list
            value: "{{tasks.generate-deployment-plan.outputs.parameters.groups_list}}"

  # 生成部署计划
  - name: generate-deployment-plan
    inputs:
      parameters:
      - name: groups_order
    script:
      image: python:3.9-alpine
      command: [python]
      source: |
        import json
        import sys
        
        # 解析有序组列表
        groups = json.loads("{{inputs.parameters.groups_order}}")
        
        if not groups:
            print("错误：没有指定部署组", file=sys.stderr)
            exit(1)
            
        # 输出完整有序组列表
        with open("/tmp/groups_list", "w") as f:
            f.write(json.dumps(groups))
    outputs:
      parameters:
      - name: groups_list
        valueFrom:
          path: /tmp/groups_list

  # 部署单个组
  - name: deploy-group
    inputs:
      parameters:
      - name: group_id
    dag:
      tasks:
      - name: build-and-deploy
        template: execute-template
        arguments:
          parameters:
          - name: template_name
            value: "java-jib-build"
          - name: group_id
            value: "{{inputs.parameters.group_id}}" 

  # 顺序处理所有组
  - name: process-groups-sequentially
    inputs:
      parameters:
      - name: groups_list
    steps:
      - - name: deploy-groups-in-order
          template: deploy-group
          withParam: "{{inputs.parameters.groups_list}}"
          arguments:
            parameters:
            - name: group_id
              value: "{{item}}"

  # 执行另一个工作流模板
  - name: execute-template
    inputs:
      parameters:
      - name: template_name
      - name: group_id
    resource:
      action: create
      manifest: |
        apiVersion: argoproj.io/v1alpha1
        kind: Workflow
        metadata:
          generateName: {{inputs.parameters.template_name}}-{{inputs.parameters.group_id}}-
          namespace: argo
        spec:
          workflowTemplateRef:
            name: {{inputs.parameters.template_name}}
          arguments:
            parameters:
            - name: app_id
              value: "{{workflow.parameters.app_id}}"
            - name: env_id
              value: "{{workflow.parameters.env_id}}"
            - name: task_id
              value: "{{workflow.parameters.task_id}}"
            - name: group_id
              value: "{{inputs.parameters.group_id}}"
            - name: image_registry
              value: "{{workflow.parameters.image_registry}}"
            - name: image_tag
              value: "{{workflow.parameters.image_tag}}"
            - name: git_username
              value: "{{workflow.parameters.git_username}}"
            - name: git_token
              value: "{{workflow.parameters.git_token}}"
            - name: deploy_url
              value: "{{workflow.parameters.deploy_url}}"
            - name: image_repo
              value: "{{workflow.parameters.image_repo}}"
            - name: image_user
              value: "{{workflow.parameters.image_user}}"
            - name: image_password
              value: "{{workflow.parameters.image_password}}"
