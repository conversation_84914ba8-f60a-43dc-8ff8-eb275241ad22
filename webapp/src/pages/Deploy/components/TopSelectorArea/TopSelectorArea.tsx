import React from 'react';
import { But<PERSON>, Tag, Tooltip } from 'antd';
import { FileOutlined, KeyOutlined, MonitorOutlined, SettingOutlined, InfoCircleOutlined, ContainerOutlined } from '@ant-design/icons';
import styles from '@/pages/Deploy/index.less';

import {
  AppInfoTooltip,
  ApplicationSelector,
  ConfigFilesTooltip,
  EnvVariablesTooltip,
  EnvironmentSelector,
  ProbeTooltip,
  MonitoringButtons
} from '@/pages/Deploy/components/index';
import { ContainerConfigTooltip } from '@/pages/Deploy/components/ContainerConfig';

import { useModel } from '@umijs/max';
import { ProbeConfig } from '@/pages/Deploy/components/probe/ProbeTooltip';

export interface TopSelectorAreaProps {
  handleOpenProbeEditor: () => void;
  handleOpenBuildConfigEditor: () => void;
  handleOpenContainerConfigEditor: () => void;
  handleOpenGroupConfigEditor: () => void;
  loading?: boolean;
  probeConfig?: ProbeConfig;
}

const TopSelectorArea: React.FC<TopSelectorAreaProps> = ({
  handleOpenProbeEditor,
  handleOpenBuildConfigEditor,
  handleOpenContainerConfigEditor,  
  handleOpenGroupConfigEditor,
  loading = false,
  probeConfig = {}
}) => {
  const { deploy } = useModel('deploy');
  
  return (
    <div className={styles.topSelectorArea}>
      <div className={styles.topSelectorContainer}>
        <div className={styles.topSelectorLeft}>
          <div className={styles.envSelectorWrapper}>
            <EnvironmentSelector />
          </div>
          <div className={styles.appSelectorWrapper}>
            <ApplicationSelector loading={loading} />
            {deploy.appData.application?.id && (
              <>
                <div className={styles.appInfoInline}>
                  {deploy.appData.application.level && (
                    <Tooltip title="应用级别">
                      <Tag color="blue">{deploy.appData.application.level}级</Tag>
                    </Tooltip>
                  )}
                  
                  {deploy.appData.application.language && (
                    <Tooltip title="开发语言">
                      <Tag color="green">{deploy.appData.application.language}</Tag>
                    </Tooltip>
                  )}
                  
                  <Tooltip title={<AppInfoTooltip appData={deploy.appData} />} color="#fff" styles={{ root: { maxWidth: '800px' } }}>
                    <Button
                      type="text"
                      size="small"
                      icon={<InfoCircleOutlined />}
                    />
                  </Tooltip>
                </div>
                
                <div className={styles.appActionButtons}>
                  <MonitoringButtons appData={deploy.appData}/>
                </div>
              </>
            )}
          </div>
        </div>
        <div className={styles.topSelectorRight}>
          <div className={styles.customNavButtons}>
            <Button.Group>
              <Tooltip 
                title={<ConfigFilesTooltip />}
                color="#fff"
                styles={{ root: { maxWidth: '800px', minWidth: '400px' } }}
              >
                <Button 
                  className="nav-button"
                  icon={<FileOutlined className="buttonIcon" />}
                >
                  <span className="buttonLabel">配置文件</span>
                </Button>
              </Tooltip>
              <Button
                className="nav-button"
                icon={<SettingOutlined className="buttonIcon" />}
                onClick={handleOpenBuildConfigEditor}
              >
                <span className="buttonLabel">Build配置</span>
              </Button>
              <Tooltip 
                title={
                  <ContainerConfigTooltip 
                    onEdit={handleOpenContainerConfigEditor}
                    editable={true}
                  />
                } 
                color="#fff" 
                styles={{ root: { maxWidth: '800px' } }}  
              >
                <Button  
                  className="nav-button"
                  icon={<ContainerOutlined className="buttonIcon" />}
                >
                  <span className="buttonLabel">容器配置</span>
                </Button>
              </Tooltip>
              <Tooltip 
                title={
                  <ProbeTooltip 
                    probeConfig={probeConfig} 
                    onEdit={handleOpenProbeEditor}
                    editable={true}
                  />
                }
                color="#fff"
                styles={{ root: { maxWidth: '600px' } }}
              >
                <Button 
                  className="nav-button"
                  icon={<MonitorOutlined className="buttonIcon" />}
                >
                  <span className="buttonLabel">探针配置</span>
                </Button>
              </Tooltip>
              <Tooltip 
                title={
                  <EnvVariablesTooltip 
                    editable={true}
                  />
                }
                color="#fff"
                styles={{ root: { maxWidth: '600px' } }}
              >
                <Button 
                  className="nav-button"
                  icon={<KeyOutlined className="buttonIcon" />}
                >
                  <span className="buttonLabel">环境变量</span>
                </Button>
              </Tooltip>
            </Button.Group>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopSelectorArea; 