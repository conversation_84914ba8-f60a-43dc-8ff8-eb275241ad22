import React, { useState, useEffect } from 'react';
import {
  Modal, Form, Button, Space,
  message, Typography, Card
} from 'antd';
import {
  RocketOutlined
} from '@ant-design/icons';
import { BuildParamsTab } from './ConfigTabs/BuildParamsTab';
import { RuntimeConfigTab } from './ConfigTabs/RuntimeConfigTab';
import { ConfigPreviewPanel } from './ConfigPreviewPanel';
import './BuildConfigEditor.less';


export interface BuildConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const BuildConfigEditor: React.FC<BuildConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewCommand, setPreviewCommand] = useState('');
  const [formValues, setFormValues] = useState<any>(null);

  useEffect(() => {
    if (visible && appData?.application) {
      const currentConfig = {
        port: 8080,
        build_params: {
          java_version: '17',
          build_tool: 'maven',
          packaging_type: 'jar',
          main_class: 'com.example.Application',
        },
        runtime_params: {
          jvm_memory: '1g',
          gc_type: 'G1GC',
          gc_tuning: '',
          jvm_options: '-Dspring.profiles.active=prod\n-Dserver.port=8080\n-Dfile.encoding=UTF-8\n-Duser.timezone=Asia/Shanghai',
          startup_command: 'java -jar /app/application.jar',
          config_file_path: '/app/config/'
        }
      };

      form.setFieldsValue(currentConfig);
      setFormValues(currentConfig);
    }
  }, [visible, appData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const buildConfig = {
        language: 'java', // 固定为Java
        port: values.port,
        build_params: values.build_params,
        runtime_params: values.runtime_params
      };

      const success = await onSave(buildConfig);
      if (success) {
        message.success('构建配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const generatePreviewCommand = () => {
    const values = form.getFieldsValue();
    const runtimeParams = values.runtime_params || {};

    let command = '';

    if (runtimeParams.jvm_memory && runtimeParams.jvm_memory !== 'auto') {
      if (runtimeParams.jvm_memory.includes('custom:')) {
        command += runtimeParams.jvm_memory.replace('custom:', '') + ' ';
      } else {
        const memory = runtimeParams.jvm_memory;
        const memoryValue = memory.replace(/[^0-9]/g, '');
        const memoryUnit = memory.replace(/[0-9]/g, '');
        command += `-Xmx${memory} -Xms${Math.floor(parseInt(memoryValue) * 0.5)}${memoryUnit} `;
      }
    }

    if (runtimeParams.gc_type) {
      if (runtimeParams.gc_type.includes('custom:')) {
        command += runtimeParams.gc_type.replace('custom:', '') + ' ';
      } else {
        switch (runtimeParams.gc_type) {
          case 'G1GC':
            command += '-XX:+UseG1GC ';
            break;
          case 'ParallelGC':
            command += '-XX:+UseParallelGC ';
            break;
          case 'ZGC':
            command += '-XX:+UseZGC ';
            break;
          case 'ShenandoahGC':
            command += '-XX:+UseShenandoahGC ';
            break;
        }
      }
    }

    if (runtimeParams.gc_tuning) {
      command += runtimeParams.gc_tuning + ' ';
    }

    if (runtimeParams.jvm_options) {
      const jvmOptions = runtimeParams.jvm_options.split('\n').filter((line: string) => line.trim());
      command += jvmOptions.join(' ') + ' ';
    }

    if (runtimeParams.startup_command) {
      command += runtimeParams.startup_command;
    } else {
      command += 'java -jar /app/application.jar';
    }

    return command.trim();
  };

  const handleFormChange = () => {
    const values = form.getFieldsValue();
    setFormValues(values);
    setPreviewCommand(generatePreviewCommand());
  };

  return (
    <Modal
      title={
          <span>Java 构建配置</span>
      }
      open={visible}
      width={1200}
      onCancel={onCancel}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          保存配置
        </Button>
      ]}
      destroyOnClose
      className="build-config-editor"
    >
      <div className="config-layout">
        <div className="config-content">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              port: 8080
            }}
            onValuesChange={handleFormChange}
          >
            <Card
              title="📋 Java构建与运行时参数"
              className="config-card"
              size="small"
              style={{ marginBottom: 16 }}
            >
              <div className="combined-params-container">
                {/* 构建参数区域 */}
                <div className="section-container">
                  <h3>构建参数</h3>
                  <BuildParamsTab
                    form={form}
                    selectedLanguage="java"
                    onFormChange={handleFormChange}
                  />
                </div>
                
                {/* 运行时参数区域 */}
                <div className="section-container">
                  <h3>运行时参数</h3>
                  <RuntimeConfigTab
                    form={form}
                    selectedLanguage="java"
                    onFormChange={handleFormChange}
                  />
                </div>
              </div>
            </Card>
          </Form>
        </div>

        <div className="preview-panel">
          <ConfigPreviewPanel
            formValues={formValues}
            selectedLanguage="java"
            previewCommand={previewCommand}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BuildConfigEditor;