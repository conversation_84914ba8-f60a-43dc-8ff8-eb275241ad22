import React, { useState, useEffect } from 'react';
import {
  Modal, Form, Button, message, Tabs, Space, Typography
} from 'antd';
import {
  BuildOutlined, RocketOutlined, SettingOutlined
} from '@ant-design/icons';
import { BuildParamsTab } from './ConfigTabs/BuildParamsTab';
import { RuntimeConfigTab } from './ConfigTabs/RuntimeConfigTab';
import './BuildConfigEditor.less';


export interface BuildConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const BuildConfigEditor: React.FC<BuildConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('build');

  useEffect(() => {
    if (visible && appData?.application) {
      const currentConfig = {
        port: 8080,
        build_params: {
          java_version: '17',
          build_tool: 'maven',
          packaging_type: 'jar',
          main_class: 'com.example.Application',
        },
        runtime_params: {
          jvm_memory: '1g',
          gc_type: 'G1GC',
          gc_tuning: '',
          jvm_options: '-Dspring.profiles.active=prod\n-Dserver.port=8080\n-Dfile.encoding=UTF-8\n-Duser.timezone=Asia/Shanghai',
          startup_command: 'java -jar /app/application.jar'
        }
      };

      form.setFieldsValue(currentConfig);
      setFormValues(currentConfig);
    }
  }, [visible, appData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const buildConfig = {
        language: 'java', // 固定为Java
        port: values.port,
        build_params: values.build_params,
        runtime_params: values.runtime_params
      };

      const success = await onSave(buildConfig);
      if (success) {
        message.success('构建配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const generatePreviewCommand = () => {
    const values = form.getFieldsValue();
    const runtimeParams = values.runtime_params || {};

    let command = '';

    if (runtimeParams.jvm_memory && runtimeParams.jvm_memory !== 'auto') {
      if (runtimeParams.jvm_memory.includes('custom:')) {
        command += runtimeParams.jvm_memory.replace('custom:', '') + ' ';
      } else {
        const memory = runtimeParams.jvm_memory;
        const memoryValue = memory.replace(/[^0-9]/g, '');
        const memoryUnit = memory.replace(/[0-9]/g, '');
        command += `-Xmx${memory} -Xms${Math.floor(parseInt(memoryValue) * 0.5)}${memoryUnit} `;
      }
    }

    if (runtimeParams.gc_type) {
      if (runtimeParams.gc_type.includes('custom:')) {
        command += runtimeParams.gc_type.replace('custom:', '') + ' ';
      } else {
        switch (runtimeParams.gc_type) {
          case 'G1GC':
            command += '-XX:+UseG1GC ';
            break;
          case 'ParallelGC':
            command += '-XX:+UseParallelGC ';
            break;
          case 'ZGC':
            command += '-XX:+UseZGC ';
            break;
          case 'ShenandoahGC':
            command += '-XX:+UseShenandoahGC ';
            break;
        }
      }
    }

    if (runtimeParams.gc_tuning) {
      command += runtimeParams.gc_tuning + ' ';
    }

    if (runtimeParams.jvm_options) {
      const jvmOptions = runtimeParams.jvm_options.split('\n').filter((line: string) => line.trim());
      command += jvmOptions.join(' ') + ' ';
    }

    if (runtimeParams.startup_command) {
      command += runtimeParams.startup_command;
    } else {
      command += 'java -jar /app/application.jar';
    }

    return command.trim();
  };

  const handleFormChange = () => {
    const values = form.getFieldsValue();
    setFormValues(values);
    setPreviewCommand(generatePreviewCommand());
  };

  const { Title } = Typography;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <div style={{
            width: 40,
            height: 40,
            borderRadius: 8,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <CodeOutlined style={{ color: 'white', fontSize: 18 }} />
          </div>
          <div>
            <Title level={4} style={{ margin: 0, color: '#1a1a1a' }}>
              Java 构建配置
            </Title>
            <div style={{ fontSize: 13, color: '#666', marginTop: 2 }}>
              配置 Java 应用的构建参数和运行时环境
            </div>
          </div>
        </div>
      }
      open={visible}
      width={1400}
      onCancel={onCancel}
      maskClosable={false}
      footer={
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px 0'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge status="processing" />
            <span style={{ fontSize: 13, color: '#666' }}>
              配置将应用于下次构建
            </span>
          </div>
          <Space size={12}>
            <Button size="large" onClick={onCancel}>
              取消
            </Button>
            <Button
              type="primary"
              size="large"
              loading={loading}
              onClick={handleSubmit}
              icon={<CheckCircleOutlined />}
            >
              保存配置
            </Button>
          </Space>
        </div>
      }
      className="modern-build-config-editor"
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 20
        },
        body: {
          padding: 0
        },
        footer: {
          borderTop: '1px solid #f0f0f0',
          marginTop: 0
        }
      }}
    >
      <div style={{
        display: 'flex',
        height: '70vh',
        background: '#fafafa'
      }}>
        {/* 左侧配置区域 */}
        <div style={{
          flex: 1,
          padding: 24,
          background: 'white',
          borderRight: '1px solid #f0f0f0',
          overflowY: 'auto'
        }}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              port: 8080
            }}
            onValuesChange={handleFormChange}
          >
            {/* 构建参数卡片 */}
            <Card
              style={{
                marginBottom: 24,
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              styles={{
                header: {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '12px 12px 0 0'
                },
                body: {
                  padding: 24
                }
              }}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <RocketOutlined style={{ color: 'white', fontSize: 16 }} />
                  <span style={{ color: 'white', fontWeight: 600 }}>构建参数</span>
                </div>
              }
            >
              <BuildParamsTab
                form={form}
                selectedLanguage="java"
                onFormChange={handleFormChange}
              />
            </Card>

            {/* 运行时参数卡片 */}
            <Card
              style={{
                borderRadius: 12,
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
              }}
              styles={{
                header: {
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  borderRadius: '12px 12px 0 0'
                },
                body: {
                  padding: 24
                }
              }}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <SettingOutlined style={{ color: 'white', fontSize: 16 }} />
                  <span style={{ color: 'white', fontWeight: 600 }}>运行时参数</span>
                </div>
              }
            >
              <RuntimeConfigTab
                form={form}
                selectedLanguage="java"
                onFormChange={handleFormChange}
              />
            </Card>
          </Form>
        </div>

        {/* 右侧预览区域 */}
        <div style={{
          width: 400,
          background: '#f8f9fa',
          padding: 24,
          overflowY: 'auto'
        }}>
          <ConfigPreviewPanel
            formValues={formValues}
            selectedLanguage="java"
            previewCommand={previewCommand}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BuildConfigEditor;