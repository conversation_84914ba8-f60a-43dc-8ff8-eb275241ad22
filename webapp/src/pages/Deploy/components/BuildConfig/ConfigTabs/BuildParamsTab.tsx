import React, { useState } from 'react';
import {
  Form, Input, Select, Radio, Switch, Row, Col, Card, Space, Typography, Tooltip
} from 'antd';
import {
  CodeOutlined, RocketOutlined, SettingOutlined, InfoCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Text, Title } = Typography;

interface BuildParamsTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const BuildParamsTab: React.FC<BuildParamsTabProps> = ({
  form,
  selectedLanguage,
  onFormChange
}) => {
  const [packagingType, setPackagingType] = useState(() => {
    return form.getFieldValue(['build_params', 'packaging_type']) || 'jar';
  });

  return (
    <div className="tab-content">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {selectedLanguage === 'java' && (
          <>
            {/* 基础配置 */}
            <div className="section-container">
              <Title level={5} style={{ marginBottom: 16, color: '#1890ff' }}>
                <CodeOutlined style={{ marginRight: 8 }} />
                基础配置
              </Title>
              <Row gutter={[24, 16]}>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>Java版本</span>
                        <Tooltip title="选择Java运行时版本">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'java_version']}
                    rules={[{ required: true, message: '请选择Java版本' }]}
                  >
                    <Select placeholder="选择Java版本" onChange={onFormChange}>
                      <Option value="8">Java 8</Option>
                      <Option value="11">Java 11</Option>
                      <Option value="17">Java 17 (推荐)</Option>
                      <Option value="21">Java 21</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>构建工具</span>
                        <Tooltip title="选择构建工具">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'build_tool']}
                  >
                    <Radio.Group buttonStyle="solid" onChange={onFormChange}>
                      <Radio.Button value="auto">自动检测</Radio.Button>
                      <Radio.Button value="maven">Maven</Radio.Button>
                      <Radio.Button value="gradle">Gradle</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>打包类型</span>
                        <Tooltip title="选择打包类型">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'packaging_type']}
                    rules={[{ required: true, message: '请选择打包类型' }]}
                  >
                    <Radio.Group buttonStyle="solid" size="large" onChange={onFormChange}>
                      <Radio.Button value="jar">JAR</Radio.Button>
                      <Radio.Button value="war">WAR</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 应用配置 */}
            <div className="section-container">
              <Title level={5} style={{ marginBottom: 16, color: '#1890ff' }}>
                <SettingOutlined style={{ marginRight: 8 }} />
                应用配置
              </Title>

              <Row gutter={[24, 16]}>
                {packagingType === 'jar' && (
                  <Col span={24}>
                    <Form.Item
                      label={
                        <Space>
                          <span>主类</span>
                          <Tooltip title="应用程序的主类，包含main方法">
                            <InfoCircleOutlined style={{ color: '#1890ff' }} />
                          </Tooltip>
                        </Space>
                      }
                      name={['build_params', 'main_class']}
                      rules={[{ required: true, message: '请输入主类' }]}
                    >
                      <Input
                        placeholder="com.example.Application"
                        onChange={onFormChange}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </div>

            {/* 构建选项 */}
            <div className="section-container">
              <Title level={5} style={{ marginBottom: 16, color: '#1890ff' }}>
                <RocketOutlined style={{ marginRight: 8 }} />
                构建选项
              </Title>

              <Row gutter={[24, 16]}>
                <Col span={8}>
                  <Form.Item
                    label="跳过测试"
                    name={['build_params', 'skip_tests']}
                    valuePropName="checked"
                  >
                    <Switch
                      checkedChildren="跳过"
                      unCheckedChildren="执行"
                      onChange={onFormChange}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="并行构建"
                    name={['build_params', 'parallel_build']}
                    valuePropName="checked"
                  >
                    <Switch
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                      onChange={onFormChange}
                    />
                  </Form.Item>
                </Col>
                {packagingType === 'war' && (
                  <>
                    <Col span={8}>
                      <Form.Item
                        label={
                          <Space>
                            <span>容器类型</span>
                            <Tooltip title="Web容器类型，目前仅支持Tomcat">
                              <InfoCircleOutlined style={{ color: '#1890ff' }} />
                            </Tooltip>
                          </Space>
                        }
                        name={['build_params', 'container_type']}
                        initialValue="tomcat"
                      >
                        <Select disabled>
                          <Option value="tomcat">Tomcat</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label={
                          <Space>
                            <span>Tomcat版本</span>
                            <Tooltip title="Tomcat容器版本">
                              <InfoCircleOutlined style={{ color: '#1890ff' }} />
                            </Tooltip>
                          </Space>
                        }
                        name={['build_params', 'tomcat_version']}
                        rules={[{ required: true, message: '请选择Tomcat版本' }]}
                      >
                        <Select
                          placeholder="选择Tomcat版本"
                          onChange={onFormChange}
                        >
                          <Option value="8.5">Tomcat 8.5</Option>
                          <Option value="9.0">Tomcat 9.0</Option>
                          <Option value="10.0">Tomcat 10.0</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </>
                )}
              </Row>
            </div>
          </>
        )}

        {selectedLanguage !== 'java' && (
          <Card
            title="构建参数配置"
            size="small"
            style={{ marginBottom: 16 }}
          >
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">
                {selectedLanguage.toUpperCase()} 语言的构建参数配置正在开发中...
              </Text>
            </div>
          </Card>
        )}
      </Space>
    </div>
  );
};
