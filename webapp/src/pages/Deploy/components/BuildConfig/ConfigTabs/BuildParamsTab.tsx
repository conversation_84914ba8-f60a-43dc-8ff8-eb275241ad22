import React, { useState } from 'react';
import {
  Form, Input, Select, Radio, Switch, Row, Col, Card, Space, Typography, Tooltip
} from 'antd';
import {
  QuestionCircleOutlined, ToolOutlined, CodeOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;

interface BuildParamsTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const BuildParamsTab: React.FC<BuildParamsTabProps> = ({
  form,
  selectedLanguage,
  onFormChange
}) => {
  const [packagingType, setPackagingType] = useState(() => {
    return form.getFieldValue(['build_params', 'packaging_type']) || 'jar';
  });


  return (
    <div className="tab-content">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {selectedLanguage === 'java' && (
          <>
            {/* Java版本和构建工具 */}
            <Card
              title={
                <Space>
                  <ToolOutlined />
                  <Text strong>Java版本和构建工具</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>Java版本</span>
                        <Text type="danger">*</Text>
                        <Tooltip title="选择Java运行时版本">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'java_version']}
                    rules={[{ required: true, message: '请选择Java版本' }]}
                  >
                    <Select size="large" placeholder="选择Java版本" onChange={onFormChange}>
                      <Option value="8">Java 8</Option>
                      <Option value="11">Java 11</Option>
                      <Option value="17">Java 17</Option>
                      <Option value="21">Java 21</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>构建工具</span>
                        <Tooltip title="选择构建工具">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'build_tool']}
                  >
                    <Radio.Group buttonStyle="solid" size="large" onChange={onFormChange}>
                      <Radio.Button value="auto">自动</Radio.Button>
                      <Radio.Button value="maven">Maven</Radio.Button>
                      <Radio.Button value="gradle">Gradle</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <Space>
                        <span>打包类型</span>
                        <Tooltip title="选择打包类型">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['build_params', 'packaging_type']}
                    rules={[{ required: true, message: '请选择打包类型' }]}
                  >
                    <Radio.Group buttonStyle="solid" size="large" onChange={onFormChange}>
                      <Radio.Button value="jar">JAR</Radio.Button>
                      <Radio.Button value="war">WAR</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* JAR包配置 */}
            {packagingType === 'jar' && (
              <Card
                title={
                  <Space>
                    <CodeOutlined />
                    <Text strong>JAR包配置</Text>
                  </Space>
                }
                size="small"
                style={{ marginBottom: 16 }}
              >
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={
                        <Space>
                          <span>主类</span>
                          <Tooltip title="应用程序的主类，包含main方法">
                            <QuestionCircleOutlined style={{ color: '#999' }} />
                          </Tooltip>
                        </Space>
                      }
                      name={['build_params', 'main_class']}
                      rules={[{ required: true, message: '请输入主类' }]}
                    >
                      <Input
                        placeholder="com.example.Application"
                        size="large"
                        onChange={onFormChange}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            )}

            {/* WAR包配置 */}
            {packagingType === 'war' && (
              <Card
                title={
                  <Space>
                    <CodeOutlined />
                    <Text strong>WAR包配置</Text>
                  </Space>
                }
                size="small"
                style={{ marginBottom: 16 }}
              >
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <Space>
                          <span>容器类型</span>
                          <Tooltip title="Web容器类型">
                            <QuestionCircleOutlined style={{ color: '#999' }} />
                          </Tooltip>
                        </Space>
                      }
                      name={['build_params', 'container_type']}
                      initialValue="tomcat"
                    >
                      <Select size="large" disabled>
                        <Option value="tomcat">Tomcat</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <Space>
                          <span>Tomcat版本</span>
                          <Tooltip title="Tomcat容器版本">
                            <QuestionCircleOutlined style={{ color: '#999' }} />
                          </Tooltip>
                        </Space>
                      }
                      name={['build_params', 'tomcat_version']}
                      rules={[{ required: true, message: '请选择Tomcat版本' }]}
                    >
                      <Select
                        size="large"
                        placeholder="选择Tomcat版本"
                        onChange={onFormChange}
                      >
                        <Option value="8.5">Tomcat 8.5</Option>
                        <Option value="9.0">Tomcat 9.0</Option>
                        <Option value="10.0">Tomcat 10.0</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            )}
          </>
        )}

        {selectedLanguage !== 'java' && (
          <Card
            title="构建参数配置"
            size="small"
            style={{ marginBottom: 16 }}
          >
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">
                {selectedLanguage.toUpperCase()} 语言的构建参数配置正在开发中...
              </Text>
            </div>
          </Card>
        )}
      </Space>
    </div>
  );
};
