import React, { useState } from 'react';
import {
  Form, Input, Select, Row, Col,
  Typography, Radio, Card, Space, Tooltip
} from 'antd';
import {
  ThunderboltOutlined, DeleteOutlined, RocketOutlined, QuestionCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface RuntimeConfigTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const RuntimeConfigTab: React.FC<RuntimeConfigTabProps> = ({
  form,
  selectedLanguage = 'java', // 添加默认值
  onFormChange
}) => {
  const [memoryMode, setMemoryMode] = useState('preset');
  const [gcMode, setGcMode] = useState('preset');

  return (
    <div className="tab-content">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Java 运行时配置 */}
        {selectedLanguage === 'java' && (
          <>
            {/* JVM内存配置 */}
            <Card
              title={
                <Space>
                  <ThunderboltOutlined />
                  <Text strong>JVM内存配置</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Form.Item
                label={
                  <Space>
                    <span>内存配置</span>
                    <Tooltip title="设置JVM堆内存大小">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name={['runtime_params', 'jvm_memory']}
              >
                <Radio.Group
                  buttonStyle="solid"
                  size="large"
                  onChange={(e) => {
                    setMemoryMode(e.target.value.includes('custom:') ? 'custom' : 'preset');
                    onFormChange();
                  }}
                >
                  <Radio.Button value="512m">512MB</Radio.Button>
                  <Radio.Button value="1g">1GB</Radio.Button>
                  <Radio.Button value="2g">2GB</Radio.Button>
                  <Radio.Button value="4g">4GB</Radio.Button>
                  <Radio.Button value="8g">8GB</Radio.Button>
                </Radio.Group>
                <div style={{ marginTop: 12 }}>
                  <Radio
                    value="custom"
                    checked={memoryMode === 'custom'}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setMemoryMode('custom');
                        form.setFieldValue(['runtime_params', 'jvm_memory'], 'custom:');
                      }
                    }}
                  >
                    自定义：
                  </Radio>
                  {memoryMode === 'custom' && (
                    <Input
                      placeholder="-Xmx2g -Xms1g -XX:MetaspaceSize=256m"
                      style={{ width: '70%', marginLeft: 8 }}
                      size="large"
                      onChange={(e) => {
                        form.setFieldValue(['runtime_params', 'jvm_memory'], `custom:${e.target.value}`);
                        onFormChange();
                      }}
                    />
                  )}
                </div>
              </Form.Item>
            </Card>

            {/* GC配置 */}
            <Card
              title={
                <Space>
                  <DeleteOutlined />
                  <Text strong>垃圾回收配置</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label={
                      <Space>
                        <span>GC策略</span>
                        <Tooltip title="选择垃圾回收器类型">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['runtime_params', 'gc_type']}
                  >
                    <Select
                      style={{ width: '100%' }}
                      defaultValue="G1GC"
                      size="large"
                      onChange={(value) => {
                        setGcMode(value.toString().includes('custom:') ? 'custom' : 'preset');
                        onFormChange();
                      }}
                    >
                      <Option value="G1GC">G1GC（推荐）</Option>
                      <Option value="ParallelGC">ParallelGC</Option>
                      <Option value="ZGC">ZGC</Option>
                      <Option value="ShenandoahGC">ShenandoahGC</Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    label={
                      <Space>
                        <span>GC调优参数</span>
                        <Tooltip title="垃圾回收器调优参数">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['runtime_params', 'gc_tuning']}
                  >
                    <Input
                      placeholder="-XX:MaxGCPauseMillis=200"
                      size="large"
                      onChange={onFormChange}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <div style={{ marginTop: 12 }}>
                <Radio
                  value="custom"
                  checked={gcMode === 'custom'}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setGcMode('custom');
                      form.setFieldValue(['runtime_params', 'gc_type'], 'custom:');
                    }
                  }}
                >
                  自定义GC参数：
                </Radio>
                {gcMode === 'custom' && (
                  <Input
                    placeholder="-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
                    style={{ width: '70%', marginLeft: 8, marginTop: 8 }}
                    size="large"
                    onChange={(e) => {
                      form.setFieldValue(['runtime_params', 'gc_type'], `custom:${e.target.value}`);
                      onFormChange();
                    }}
                  />
                )}
              </div>
            </Card>

            {/* JVM启动参数 */}
            <Card
              title={
                <Space>
                  <RocketOutlined />
                  <Text strong>JVM启动参数</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Form.Item
                label={
                  <Space>
                    <span>JVM启动参数</span>
                    <Tooltip title="JVM启动时的系统参数">
                      <QuestionCircleOutlined style={{ color: '#999' }} />
                    </Tooltip>
                  </Space>
                }
                name={['runtime_params', 'jvm_options']}
                extra="每行一个参数，支持Spring Boot配置参数"
              >
                <TextArea
                  placeholder={`-Dspring.profiles.active=prod
-Dserver.port=8080
-Dfile.encoding=UTF-8
-Djava.security.egd=file:/dev/./urandom
-Duser.timezone=Asia/Shanghai`}
                  autoSize={{ minRows: 4, maxRows: 6 }}
                  onChange={onFormChange}
                />
              </Form.Item>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label={
                      <Space>
                        <span>启动命令</span>
                        <Tooltip title="应用启动命令">
                          <QuestionCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      </Space>
                    }
                    name={['runtime_params', 'startup_command']}
                  >
                    <Input
                      placeholder="java -jar /app/application.jar"
                      size="large"
                      onChange={onFormChange}
                    />
                  </Form.Item>
                </Col>


              </Row>
            </Card>
          </>
        )}

        {/* 非Java语言的运行时配置 */}
        {selectedLanguage !== 'java' && (
          <Card
            title={
              <Space>
                <RocketOutlined />
                <Text strong>运行时配置</Text>
              </Space>
            }
            size="small"
            style={{ marginBottom: 16 }}
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={
                    <Space>
                      <span>内存分配</span>
                      <Tooltip title="应用运行时内存限制">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  }
                  name={['runtime_params', 'memory_limit']}
                >
                  <Select size="large" onChange={onFormChange}>
                    <Option value="auto">自动分配</Option>
                    <Option value="512m">512MB</Option>
                    <Option value="1g">1GB</Option>
                    <Option value="2g">2GB</Option>
                    <Option value="4g">4GB</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={16}>
                <Form.Item
                  label={
                    <Space>
                      <span>启动命令</span>
                      <Tooltip title="应用启动命令">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  }
                  name={['runtime_params', 'startup_command']}
                >
                  <Input
                    placeholder="启动命令"
                    size="large"
                    onChange={onFormChange}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}
      </Space>
    </div>
  );
};
