import React from 'react';
import { 
  <PERSON>, Typography, <PERSON>, Button, Alert, 
  Divider, Tag, List, Tooltip
} from 'antd';
import { 
  CopyOutlined, CheckCircleOutlined, 
  ExclamationCircleOutlined, InfoCircleOutlined,
  RocketOutlined, DatabaseOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface ConfigPreviewPanelProps {
  formValues: any;
  selectedLanguage: string;
  previewCommand: string;
}

export const ConfigPreviewPanel: React.FC<ConfigPreviewPanelProps> = ({
  formValues,
  selectedLanguage,
  previewCommand
}) => {
  
  // 生成Jib构建命令预览
  const generateJibCommand = () => {
    if (!formValues) return '';

    const buildParams = formValues.build_params || {};

    if (selectedLanguage === 'java') {
      let command = '';

      if (buildParams.build_tool === 'maven') {
        command = 'mvn compile jib:build';
      } else if (buildParams.build_tool === 'gradle') {
        command = './gradlew jib';
      }

      return command;
    }

    return '';
  };

  // 生成K8s部署配置
  const generateK8sConfig = () => {
    if (!formValues) return '';

    const runtimeParams = formValues.runtime_params || {};
    const port = formValues.port || 8080;

    let config = `image: app:latest
ports:
  - containerPort: ${port}`;

    return config;
  };

  // 生成配置摘要
  const generateConfigSummary = () => {
    if (!formValues) return [];

    const buildParams = formValues.build_params || {};
    const runtimeParams = formValues.runtime_params || {};

    const summary = [];

    // 语言和版本
    if (selectedLanguage === 'java' && buildParams.java_version) {
      summary.push(`语言: Java ${buildParams.java_version}`);
    } else if (selectedLanguage && buildParams[`${selectedLanguage}_version`]) {
      summary.push(`语言: ${selectedLanguage.toUpperCase()} ${buildParams[`${selectedLanguage}_version`]}`);
    }

    // 构建工具
    if (buildParams.build_tool) {
      summary.push(`构建工具: ${buildParams.build_tool.charAt(0).toUpperCase() + buildParams.build_tool.slice(1)}`);
    }

    // 打包类型
    if (buildParams.packaging_type) {
      summary.push(`打包类型: ${buildParams.packaging_type.toUpperCase()}`);
    }

    // 内存配置
    if (runtimeParams.jvm_memory) {
      const memory = runtimeParams.jvm_memory.includes('custom:')
        ? '自定义配置'
        : runtimeParams.jvm_memory;
      summary.push(`内存配置: ${memory}`);
    }

    // GC策略
    if (runtimeParams.gc_type && selectedLanguage === 'java') {
      summary.push(`GC策略: ${runtimeParams.gc_type}`);
    }

    // 端口配置
    if (formValues.port) {
      summary.push(`端口: ${formValues.port}`);
    }

    // 主类配置
    if (buildParams.packaging_type === 'jar' && buildParams.main_class) {
      summary.push(`主类: ${buildParams.main_class}`);
    }

    // Tomcat版本
    if (buildParams.packaging_type === 'war' && buildParams.tomcat_version) {
      summary.push(`Tomcat: ${buildParams.tomcat_version}`);
    }

    return summary;
  };

  // 生成验证状态
  const generateValidationStatus = () => {
    if (!formValues) return [];

    const validations = [];
    const buildParams = formValues.build_params || {};
    const runtimeParams = formValues.runtime_params || {};

    // 基础配置验证
    if (formValues.language) {
      validations.push({ type: 'success', message: '基础配置完成' });
    }

    // 构建参数验证
    if (selectedLanguage === 'java' && buildParams.java_version) {
      validations.push({ type: 'success', message: 'Java版本兼容' });
    }

    if (buildParams.build_tool) {
      validations.push({ type: 'success', message: '构建工具配置有效' });
    }

    // 打包类型验证
    if (buildParams.packaging_type) {
      validations.push({ type: 'success', message: '打包类型已选择' });
    }

    // 主类验证
    if (buildParams.packaging_type === 'jar' && buildParams.main_class) {
      validations.push({ type: 'success', message: '主类配置正确' });
    }

    // Tomcat版本验证
    if (buildParams.packaging_type === 'war' && buildParams.tomcat_version) {
      validations.push({ type: 'success', message: 'Tomcat版本已配置' });
    }

    // 内存配置验证
    if (runtimeParams.jvm_memory) {
      validations.push({ type: 'success', message: '内存配置合理' });
    }

    // 警告和建议
    if (buildParams.packaging_type === 'jar' && !buildParams.main_class) {
      validations.push({ type: 'warning', message: '建议配置主类' });
    }

    if (buildParams.packaging_type === 'war' && !buildParams.tomcat_version) {
      validations.push({ type: 'warning', message: '建议选择Tomcat版本' });
    }

    // 可选配置
    if (!runtimeParams.jvm_options) {
      validations.push({ type: 'info', message: 'JVM参数可选配置' });
    }

    return validations;
  };

  const jibCommand = generateJibCommand();
  const k8sConfig = generateK8sConfig();
  const configSummary = generateConfigSummary();
  const validationStatus = generateValidationStatus();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="config-preview-panel">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* Java运行时命令预览 */}
        {previewCommand && selectedLanguage === 'java' && (
          <Card 
            title={
              <Space>
                <RocketOutlined />
                <Text strong>运行时命令</Text>
              </Space>
            }
            size="small"
            extra={
              <Tooltip title="复制命令">
                <Button 
                  type="text" 
                  icon={<CopyOutlined />} 
                  size="small"
                  onClick={() => copyToClipboard(previewCommand)}
                />
              </Tooltip>
            }
          >
            <Paragraph 
              code 
              style={{ 
                margin: 0, 
                fontSize: '12px',
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {previewCommand}
            </Paragraph>
          </Card>
        )}

        {/* Jib构建命令 */}
        {jibCommand && (
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                <Text strong>Jib构建命令</Text>
              </Space>
            }
            size="small"
            extra={
              <Tooltip title="复制命令">
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  size="small"
                  onClick={() => copyToClipboard(jibCommand)}
                />
              </Tooltip>
            }
          >
            <Paragraph
              code
              style={{
                margin: 0,
                fontSize: '12px',
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {jibCommand}
            </Paragraph>
          </Card>
        )}

        {/* 配置摘要 */}
        <Card 
          title={
            <Space>
              <InfoCircleOutlined />
              <Text strong>配置摘要</Text>
            </Space>
          }
          size="small"
        >
          <List
            size="small"
            dataSource={configSummary}
            renderItem={(item) => (
              <List.Item style={{ padding: '4px 0', border: 'none' }}>
                <Text style={{ fontSize: '12px' }}>• {item}</Text>
              </List.Item>
            )}
          />
        </Card>
      </Space>
    </div>
  );
};
