.build-config-editor {
  .ant-modal-body {
    padding: 16px;
  }

  // 主布局容器
  .config-layout {
    display: flex;
    gap: 16px;
    min-height: 600px;

    // 主配置区域
    .config-content {
      flex: 1;
      min-width: 0; // 防止flex子项溢出

      // 基础配置卡片
      .basic-config-card {
        .ant-card-head {
          padding: 12px 16px;
          min-height: auto;
          border-bottom: 1px solid #f0f0f0;

          .ant-card-head-title {
            font-size: 14px;
            font-weight: 600;
          }
        }

        .ant-card-body {
          padding: 16px;
        }

        .basic-config-content {
          .ant-form-item {
            margin-bottom: 16px;
          }
        }
      }
      
      // 合并后的配置卡片
      .config-card {
        .ant-card-head {
          padding: 12px 16px;
          min-height: auto;
          border-bottom: 1px solid #f0f0f0;

          .ant-card-head-title {
            font-size: 14px;
            font-weight: 600;
          }
        }

        .ant-card-body {
          padding: 16px;
        }
        
        .combined-params-container {
          display: flex;
          flex-direction: column;
          gap: 24px;
          
          .section-container {
            h3 {
              font-size: 15px;
              font-weight: 500;
              margin-bottom: 16px;
              padding-bottom: 8px;
              border-bottom: 1px dashed #f0f0f0;
            }
            
            .ant-form-item {
              margin-bottom: 16px;
            }
          }
        }
      }

      .config-tabs {
        .ant-tabs-nav {
          margin-bottom: 16px;

          .ant-tabs-tab {
            padding: 8px 16px;
            font-weight: 500;

            &.ant-tabs-tab-active {
              .ant-tabs-tab-btn {
                color: #1890ff;
                font-weight: 600;
              }
            }
          }
        }

        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              padding: 0;
            }
          }
        }
      }
    }

    // 右侧预览面板
    .preview-panel {
      width: 360px;
      flex-shrink: 0;

      .config-preview-panel {
        .ant-card {
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .ant-card-head {
            padding: 8px 12px;
            min-height: auto;

            .ant-card-head-title {
              font-size: 13px;
              font-weight: 600;
            }

            .ant-card-extra {
              font-size: 12px;
            }
          }

          .ant-card-body {
            padding: 12px;

            .ant-typography {
              margin-bottom: 0;

              &.ant-typography-caption {
                font-size: 11px;
                line-height: 1.4;
              }
            }

            .ant-list {
              .ant-list-item {
                padding: 2px 0;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  // Tab内容区域样式
  .tab-content {
    .ant-form-item {
      margin-bottom: 16px;
      
      .ant-form-item-label {
        padding-bottom: 4px;
        
        label {
          font-size: 13px;
          font-weight: 500;
        }
      }
      
      .ant-form-item-control {
        .ant-form-item-explain {
          font-size: 11px;
          margin-top: 4px;
        }
      }
    }
    
    .ant-card {
      margin-bottom: 16px;
      border: 1px solid #f0f0f0;
      
      .ant-card-head {
        padding: 12px 16px;
        min-height: auto;
        border-bottom: 1px solid #f0f0f0;
        
        .ant-card-head-title {
          font-size: 14px;
          font-weight: 600;
        }
      }
      
      .ant-card-body {
        padding: 16px;
      }
    }
    
    // 单选按钮组样式
    .ant-radio-group {
      &.ant-radio-group-solid {
        .ant-radio-button-wrapper {
          border-radius: 4px;
          margin-right: 8px;
          border: 1px solid #d9d9d9;
          
          &:not(:first-child) {
            margin-left: 0;
          }
          
          &.ant-radio-button-wrapper-checked {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
            
            &:hover {
              background: #40a9ff;
              border-color: #40a9ff;
            }
          }
        }
      }
    }
    
    // 开关样式
    .ant-switch {
      &.ant-switch-checked {
        background-color: #52c41a;
      }
    }
    
    // 文本域样式
    .ant-input {
      &.ant-input-lg {
        font-size: 14px;
      }
    }
    
    .ant-select {
      &.ant-select-lg {
        .ant-select-selector {
          font-size: 14px;
        }
      }
    }
    
    // 代码文本域样式
    textarea.ant-input {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.4;
      
      &::placeholder {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        color: #bfbfbf;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1400px) {
    .config-layout {
      flex-direction: column;

      .preview-panel {
        width: 100%;
        order: -1; // 移动端时预览面板显示在上方
      }
    }
  }

  @media (max-width: 768px) {
    .config-layout {
      .config-content {
        .basic-config-card {
          .basic-config-content {
            .ant-row {
              .ant-col {
                margin-bottom: 16px;
              }
            }
          }
        }
      }
    }
  }
}

// 预览面板特定样式
.config-preview-panel {
  .ant-typography {
    &.ant-typography-caption {
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 8px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      white-space: pre-wrap;
      word-break: break-all;
      margin: 0;
    }
  }
  
  .validation-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    
    .anticon {
      margin-right: 6px;
      font-size: 12px;
    }
    
    .ant-typography {
      margin: 0;
      font-size: 12px;
    }
  }
}

// 工具提示样式
.ant-tooltip {
  .ant-tooltip-inner {
    font-size: 12px;
  }
}

// 表单验证样式
.ant-form-item-has-error {
  .ant-form-item-explain {
    color: #ff4d4f;
  }
}

.ant-form-item-has-warning {
  .ant-form-item-explain {
    color: #faad14;
  }
}
