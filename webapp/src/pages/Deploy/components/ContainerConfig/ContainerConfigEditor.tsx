import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal, Form, Input, Button,
  Card, Row, Col, message, Select,
  Typography, Space, Divider, Tabs, Tooltip, Badge
} from 'antd';
import {
  PlusOutlined, DeleteOutlined, SettingOutlined,
  DatabaseOutlined, CloudServerOutlined, DeploymentUnitOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import useContainerConfig from '@/hooks/useContainerConfig';
import { BasicConfigTab, StrategyConfigTab, NetworkConfigTab } from './ConfigTabs';
import './ContainerConfigEditor.less';

const { Text } = Typography;
const { Option } = Select;

export interface ContainerConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const ContainerConfigEditor: React.FC<ContainerConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState<string>('blueGreen');
  const [activeTab, setActiveTab] = useState<string>('basic');
  const { containerConfig, saveContainerConfig } = useContainerConfig();

  // 字段变化追踪，用于高亮显示已修改的字段
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // 记录字段变化
  const handleFieldChange = (changedFields: any) => {
    const newTouchedFields = new Set(touchedFields);
    Object.keys(changedFields).forEach(field => {
      if (typeof field === 'string') {
        newTouchedFields.add(field);
      }
    });
    setTouchedFields(newTouchedFields);
  };

  useEffect(() => {
    if (visible && containerConfig) {
      const strategy = containerConfig.strategy || 'blueGreen';
      setSelectedStrategy(strategy);

      form.setFieldsValue({
        ...containerConfig,
        strategy,
        // 确保嵌套对象正确设置
        resources: containerConfig.resources || {
          requests: { cpu: '500m', memory: '512Mi' },
          limits: { cpu: '1000m', memory: '1Gi' }
        },
        ports: containerConfig.ports || [
          { name: 'http', container_port: 8080, service_port: 8080, protocol: 'TCP' }
        ],
        rollout_config: containerConfig.rollout_config || {
          max_surge: '25%',
          max_unavailable: '25%',
          revision_history_limit: 10,
          timeout_seconds: 600
        },
        // 蓝绿部署配置
        bluegreen_config: containerConfig.bluegreen_config || {
          auto_promotion_enabled: false,
          scale_down_delay_seconds: 30,
          preview_replica_count: 1
        },
        // 金丝雀部署配置
        canary_config: containerConfig.canary_config || {
          steps: [
            { set_weight: 20, pause_duration: 0 },
            { set_weight: 50, pause_duration: 0 },
            { set_weight: 100, pause_duration: 0 }
          ],
          traffic_routing: {
            istio: {
              virtual_service: {
                name: '',
                routes: []
              }
            }
          }
        }
      });
    }
  }, [visible, containerConfig, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const success = await saveContainerConfig(values);
      if (success) {
        message.success('容器配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const addPort = useCallback(() => {
    const ports = form.getFieldValue('ports') || [];
    ports.push({
      name: `port-${ports.length + 1}`,
      container_port: 8080,
      service_port: 8080,
      protocol: 'TCP'
    });
    form.setFieldsValue({ ports });
  }, [form]);

  const removePort = useCallback((index: number) => {
    const ports = form.getFieldValue('ports') || [];
    if (ports.length > 1) {
      ports.splice(index, 1);
      form.setFieldsValue({ ports });
    }
  }, [form]);

  const handleStrategyChange = useCallback((strategy: string) => {
    setSelectedStrategy(strategy);
    form.setFieldsValue({ strategy });
    setActiveTab('strategy'); // 自动切换到部署策略页签
  }, [form]);
  
  // 使用imported deployment diagrams
  
  // 环境变量Tab的内容
  const renderEnvironmentTabContent = () => (
    <div className="tab-content">
      <Card 
        className="config-card"
        bordered={false}
        bodyStyle={{ padding: '20px', backgroundColor: '#f9fafc', borderRadius: '8px' }}
      >
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          backgroundColor: '#fcf4ff',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #722ed1'
        }}>
          <DeploymentUnitOutlined style={{ fontSize: '18px', color: '#722ed1', marginRight: '10px' }} />
          <Typography.Title level={5} style={{ margin: 0, color: '#531dab' }}>容器环境变量配置</Typography.Title>
        </div>

        <div style={{ backgroundColor: '#fff', borderRadius: '6px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }}>
          <Form.Item
            name="environment"
            label={
              <div className="env-label" style={{ marginBottom: '8px' }}>
                <Space>
                  <span style={{ fontSize: '15px', fontWeight: 500 }}>环境变量 (JSON格式)</span>
                  <Tooltip title="输入合法的JSON格式键值对，用于设置容器内环境变量">
                    <InfoCircleOutlined style={{ color: '#722ed1' }} />
                  </Tooltip>
                </Space>
              </div>
            }
            rules={[
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  try {
                    JSON.parse(value);
                    return Promise.resolve();
                  } catch {
                    return Promise.reject(new Error('请输入有效的JSON格式'));
                  }
                }
              }
            ]}
          >
            <Input.TextArea
              rows={10}
              placeholder='{"ENV_NAME": "value", "DEBUG": "true", "SERVER_PORT": "8080"}'
              className="env-textarea"
              style={{ 
                backgroundColor: '#fff', 
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                fontFamily: 'monospace'
              }}
            />
          </Form.Item>
          
          <div className="env-tips" style={{ marginTop: '12px', fontSize: '13px', padding: '12px', backgroundColor: '#f9f0ff', borderRadius: '6px', border: '1px solid #d3adf7' }}>
            <Typography.Text type="secondary">
              <InfoCircleOutlined style={{marginRight: '8px', color: '#722ed1' }} />
              环境变量将以键值对形式注入到容器中，可用于配置应用程序的运行参数
            </Typography.Text>
          </div>
        </div>
      </Card>

      <Card 
        className="config-card"
        bordered={false}
        bodyStyle={{ padding: '20px', backgroundColor: '#f9fafc', borderRadius: '8px' }}
        style={{ marginTop: '20px' }}
      >
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          backgroundColor: '#f6f6f6',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #8c8c8c'
        }}>
          <SettingOutlined style={{ fontSize: '18px', color: '#8c8c8c', marginRight: '10px' }} />
          <Typography.Title level={5} style={{ margin: 0, color: '#595959' }}>环境变量示例</Typography.Title>
        </div>

        <div style={{ backgroundColor: '#fff', borderRadius: '6px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }}>
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <Card 
                size="small" 
                title={
                  <Space>
                    <Badge color="#1890ff" />
                    <span>基础环境变量</span>
                  </Space>
                }
                className="example-card"
                style={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
              >
                <pre className="code-example" style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '13px',
                  overflow: 'auto',
                  maxHeight: '200px'
                }}>
{`{
  "DEBUG": "false",
  "LOG_LEVEL": "info"
}`}
                </pre>
              </Card>
            </Col>
            <Col span={12}>
              <Card 
                size="small" 
                title={
                  <Space>
                    <Badge color="#52c41a" />
                    <span>应用配置示例</span>
                  </Space>
                }
                className="example-card"
                style={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
              >
                <pre className="code-example" style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  fontSize: '13px',
                  overflow: 'auto',
                  maxHeight: '200px'
                }}>
{`{
  "PORT": "8080",
  "DB_HOST": "mysql",
  "MODE": "production"
}`}
                </pre>
              </Card>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  );
  
  // 发布配置Tab的内容
  const renderRolloutTabContent = () => (
    <div className="tab-content">
      <div className="rollout-section">
        <Typography.Paragraph>
          <Text type="secondary" className="section-description">
            配置应用发布和滚动更新的方式和速率
          </Text>
          <Tooltip title="了解更多发布配置">
            <InfoCircleOutlined className="help-icon" />
          </Tooltip>
        </Typography.Paragraph>

        <Card className="rollout-card">
          <Row gutter={[24, 16]}>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_surge']}
                label={
                  <span>
                    最大激增
                    <Tooltip title="更新过程中可以创建的超出期望副本数的Pod数量">
                      <InfoCircleOutlined style={{marginLeft: '4px'}} />
                    </Tooltip>
                  </span>
                }
                rules={[
                  { required: true, message: '请输入最大激增' },
                  { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                ]}
              >
                <Input placeholder="25%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'max_unavailable']}
                label={
                  <span>
                    最大不可用
                    <Tooltip title="更新过程中允许的不可用Pod的最大数量">
                      <InfoCircleOutlined style={{marginLeft: '4px'}} />
                    </Tooltip>
                  </span>
                }
                rules={[
                  { required: true, message: '请输入最大不可用' },
                  { pattern: /^(\d+%?|\d+)$/, message: '格式不正确' }
                ]}
              >
                <Input placeholder="25%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'revision_history_limit']}
                label={
                  <span>
                    历史版本
                    <Tooltip title="保留的历史ReplicaSet数量">
                      <InfoCircleOutlined style={{marginLeft: '4px'}} />
                    </Tooltip>
                  </span>
                }
                rules={[
                  { type: 'integer', min: 1, max: 100, message: '必须在1-100之间' }
                ]}
              >
                <Select placeholder="选择历史版本数量">
                  {[...Array(10)].map((_, i) => (
                    <Option key={i + 1} value={i + 1}>{i + 1}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['rollout_config', 'timeout_seconds']}
                label={
                  <span>
                    超时时间(秒)
                    <Tooltip title="部署操作的超时时间">
                      <InfoCircleOutlined style={{marginLeft: '4px'}} />
                    </Tooltip>
                  </span>
                }
                rules={[
                  { type: 'integer', min: 60, max: 3600, message: '必须在60-3600秒之间' }
                ]}
              >
                <Select placeholder="选择超时时间">
                  <Option value={60}>1分钟</Option>
                  <Option value={300}>5分钟</Option>
                  <Option value={600}>10分钟</Option>
                  <Option value={1800}>30分钟</Option>
                  <Option value={3600}>60分钟</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <div className="rollout-tips">
          <Typography.Paragraph>
            <Text strong>发布配置说明:</Text>
          </Typography.Paragraph>
          <ul className="tips-list">
            <li>
              <Text>
                <Badge color="blue" />
                <strong>最大激增:</strong> 更新过程中可以超出期望副本数的最大Pod数量，可以是数字或百分比
              </Text>
            </li>
            <li>
              <Text>
                <Badge color="blue" />
                <strong>最大不可用:</strong> 更新过程中允许不可用的最大Pod数量，可以是数字或百分比
              </Text>
            </li>
            <li>
              <Text>
                <Badge color="blue" />
                <strong>历史版本数:</strong> 保留的历史ReplicaSet数量，用于回滚操作
              </Text>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );

  // 定义Tab项目
  const tabItems = [
    {
      key: 'basic',
      label: (
        <Tooltip title="基本信息和资源配置">
          <span>基础配置</span>
        </Tooltip>
      ),
      children: <BasicConfigTab form={form} />
    },
    {
      key: 'strategy',
      label: (
        <Tooltip title="部署策略和流量控制">
          <span>部署策略</span>
        </Tooltip>
      ),
      children: <StrategyConfigTab 
                  selectedStrategy={selectedStrategy} 
                  onStrategyChange={handleStrategyChange} 
                />
    },
    {
      key: 'network',
      label: (
        <Tooltip title="网络端口和服务设置">
          <span>网络设置</span>
        </Tooltip>
      ),
      children: <NetworkConfigTab addPort={addPort} removePort={removePort} />
    },
    {
      key: 'environment',
      label: (
        <Tooltip title="配置容器中使用的环境变量">
          <span>容器环境变量</span>
        </Tooltip>
      ),
      children: renderEnvironmentTabContent()
    }
  ];

  if (!appData) {
    return null;
  }

  return (
    <Modal
      title={
        <div className="modal-title">
          <Space>
            <span>容器配置</span>
          </Space>
        </div>
      }
      open={visible}
      width={900}
      onCancel={onCancel}
      bodyStyle={{ padding: '8px 12px' }}
      footer={
        <div className="modal-footer">
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              保存配置
            </Button>
          </Space>
        </div>
      }
      destroyOnClose
      className="container-config-editor"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={containerConfig || undefined}
        onValuesChange={handleFieldChange}
      >
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={tabItems}
          className="config-tabs"
          size="middle"
          type="card"
          tabBarStyle={{ marginBottom: '16px', marginTop: '-4px' }}
        />
      </Form>
    </Modal>
  );
};

export default ContainerConfigEditor; 