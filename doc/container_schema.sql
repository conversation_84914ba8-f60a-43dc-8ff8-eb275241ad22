-- t_container 容器配置表

CREATE TABLE `t_container` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` bigint NOT NULL COMMENT '应用ID',
  `env_id` bigint NOT NULL COMMENT '环境ID', 
  `group_id` bigint NOT NULL COMMENT '分组ID',
  `base_image` varchar(500) NOT NULL COMMENT '基础镜像',
  `replicas` int NOT NULL DEFAULT '1' COMMENT '副本数量',
  `namespace` varchar(100) NOT NULL DEFAULT 'default' COMMENT 'K8S命名空间',
  `ports` json DEFAULT NULL COMMENT '端口配置，JSON格式存储',
  `resources` json NOT NULL COMMENT '资源配置，JSON格式存储',
  `environment` json DEFAULT NULL COMMENT '环境变量配置，JSON格式存储',
  `strategy` varchar(50) NOT NULL DEFAULT 'blueGreen' COMMENT '部署策略：blueGreen/canary/rolling',
  `rollout_config` json NOT NULL COMMENT '发布配置，JSON格式存储',
  `override` json DEFAULT NULL COMMENT '覆盖配置，JSON格式存储',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  
  -- 审计字段
  `c_t` bigint NOT NULL COMMENT '创建时间戳',
  `create_by` bigint NOT NULL COMMENT '创建人ID',
  `u_t` bigint NOT NULL COMMENT '更新时间戳', 
  `update_by` bigint NOT NULL COMMENT '更新人ID',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-正常 1-删除',
  
  PRIMARY KEY (`id`),
  KEY `idx_app_env_group` (`app_id`, `env_id`, `group_id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_create_time` (`c_t`),
  KEY `idx_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='容器配置表'; 