{"version": 3, "file": "diff.js", "sourceRoot": "", "sources": ["../src/diff.tsx"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAK,MAAM,MAAM,wCAAwC,CAAC;AACjE,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAE5C,SAAS,gBAAgB,CAAC,EAiBF;QAhBtB,aAAc,EAAd,KAAK,mBAAG,MAAM,KAAA,EACd,cAAe,EAAf,MAAM,mBAAG,MAAM,KAAA,EACf,aAAY,EAAZ,KAAK,mBAAG,IAAI,KAAA,EACZ,oBAAiB,EAAjB,YAAY,mBAAG,EAAE,KAAA,EACjB,gBAAuB,EAAvB,QAAQ,mBAAG,YAAY,KAAA,EACvB,aAAY,EAAZ,KAAK,mBAAG,IAAI,KAAA,EACZ,eAAY,EAAZ,OAAO,mBAAG,EAAE,KAAA,EACZ,wBAAqB,EAArB,gBAAgB,mBAAG,EAAE,KAAA,EACrB,uBAAsB,EAAtB,eAAe,mBAAG,IAAI,KAAA,EACtB,sBAAqB,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,yBAAwB,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,EACxB,gBAAe,EAAf,QAAQ,mBAAG,IAAI,KAAA,EACf,iBAAgB,EAAhB,SAAS,mBAAG,IAAI,KAAA,EAChB,gBAAe,EAAf,QAAQ,mBAAG,IAAI,KAAA,EACf,WAAW,iBAAA,EACX,WAAW,iBAAA;IAEX,IAAM,gBAAgB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAE7D,IAAM,MAAM,GAAG,MAAM,CAA6C,IAAI,CAAC,CAAC;IAExE,IAAM,aAAa,GAAG,MAAM,CAA4B,IAAI,CAAC,CAAC;IAE9D,IAAM,8BAA8B,GAAG,MAAM,CAAiB,IAAI,CAAC,CAAC;IAEpE,IAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IAEtC,IAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAExC,IAAM,KAAK,GAAG,OAAO,CACnB,cAAM,OAAA,CAAC;QACL,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;KACpB,CAAC,EAHI,CAGJ,EACF,CAAC,UAAU,EAAE,WAAW,CAAC,CAC1B,CAAC;IAEF,IAAM,qBAAqB,GAAG;QAC5B,IAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,YAAY,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAM,oBAAoB,GAAG;QAC3B,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE/B,IAAA,QAAQ,GAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,SAA9B,CAA+B;QAC/C,aAAa,CAAC,OAAO,GAAG,QAAQ,CAAC,kBAAkB,CAAC,UAAC,KAAK;YACxD,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC;gBAC5C,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAM,uBAAuB,GAAG;QAC9B,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAM,UAAU,GAAG;QACjB,IAAM,UAAU,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;QACxD,IAAM,gBAAgB,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,MAAM,CAAC,CAAC;QAC/C,IAAM,gBAAgB,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,MAAM,CAAC,CAAC;QAC/C,IAAI,aAAa,GACf,gBAAgB,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAC/D,IAAI,aAAa,GACf,gBAAgB,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAE/D,8CAA8C;QAC9C,kEAAkE;QAClE,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CACvC,UAAU,EACV,QAAQ,EACR,gBAAgB,CACjB,CAAC;QACJ,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CACvC,UAAU,EACV,QAAQ,EACR,gBAAgB,CACjB,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtB,QAAQ,EAAE,aAAa;YACvB,QAAQ,EAAE,aAAa;SACxB,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,SAAS,CACP;QACE,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,oCAAoC;YACpC,qBAAqB,EAAE,CAAC;YACxB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC7C,gBAAgB,CAAC,OAAO,iCAEnB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,oBAAoB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GACtD,OAAO,GACP,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAE7B,gBAAgB,CACjB,CAAC;YACF,mCAAmC;YACnC,UAAU,EAAE,CAAC;YACb,oBAAoB,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IACD,uDAAuD;IACvD,EAAE,CACH,CAAC;IAEF,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,CAAC,OAAO,CAAC,aAAa,uBACvB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,oBAAoB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GACtD,OAAO,EACV,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAEzB,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAEpB,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACb,IAAA,KAAyC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAtD,cAAc,cAAA,EAAE,QAAQ,cAA8B,CAAC;YACzE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACrD,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACX,IAAA,QAAQ,GAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,SAA9B,CAA+B;YAC/C,8BAA8B,CAAC,OAAO,GAAG,IAAI,CAAC;YAC9C,2DAA2D;YAC3D,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,YAAY,EAAE,CAAC;YAClD,qFAAqF;YACrF,mBAAmB;YACnB,QAAQ,CAAC,kBAAkB,CACzB,EAAE,EACF;gBACE;oBACE,KAAK,EAAE,QAAQ,CAAC,iBAAiB,EAAE;oBACnC,IAAI,EAAE,KAAK;iBACZ;aACF,CACF,CAAC;YACF,2DAA2D;YAC3D,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,YAAY,EAAE,CAAC;YAClD,8BAA8B,CAAC,OAAO,GAAG,KAAK,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,SAAS,CAAC;QACR,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACX,IAAU,cAAc,GAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,SAA9B,CAA+B;YAC/D,IAAI,QAAQ,KAAK,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC;gBAC3C,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,SAAS,CACP,cAAM,OAAA;QACJ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,uBAAuB,EAAE,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACnB,IAAA,KACJ,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,EADT,cAAc,cAAA,EAAE,QAAQ,cACf,CAAC;YAC5B,IAAI,cAAc,EAAE,CAAC;gBACnB,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACb,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QACD,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1B,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;IACH,CAAC,EAhBK,CAgBL;IACD,uDAAuD;IACvD,EAAE,CACH,CAAC;IAEF,OAAO,CACL,6BACE,GAAG,EAAE,gBAAgB,EACrB,KAAK,EAAE,KAAK,EACZ,SAAS,EAAC,+BAA+B,GACzC,CACH,CAAC;AACJ,CAAC;AAED,gBAAgB,CAAC,WAAW,GAAG,kBAAkB,CAAC;AAElD,eAAe,gBAAgB,CAAC"}