{"version": 3, "file": "editor.js", "sourceRoot": "", "sources": ["../src/editor.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAK,MAAM,MAAM,wCAAwC,CAAC;AACjE,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAE5C,SAAS,YAAY,CAAC,EAeF;QAdlB,aAAc,EAAd,KAAK,mBAAG,MAAM,KAAA,EACd,cAAe,EAAf,MAAM,mBAAG,MAAM,KAAA,EACf,aAAY,EAAZ,KAAK,mBAAG,IAAI,KAAA,EACZ,oBAAiB,EAAjB,YAAY,mBAAG,EAAE,KAAA,EACjB,gBAAuB,EAAvB,QAAQ,mBAAG,YAAY,KAAA,EACvB,aAAY,EAAZ,KAAK,mBAAG,IAAI,KAAA,EACZ,eAAY,EAAZ,OAAO,mBAAG,EAAE,KAAA,EACZ,wBAAqB,EAArB,gBAAgB,mBAAG,EAAE,KAAA,EACrB,uBAAsB,EAAtB,eAAe,mBAAG,IAAI,KAAA,EACtB,sBAAqB,EAArB,cAAc,mBAAG,IAAI,KAAA,EACrB,yBAAwB,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,EACxB,gBAAe,EAAf,QAAQ,mBAAG,IAAI,KAAA,EACf,iBAAgB,EAAhB,SAAS,mBAAG,IAAI,KAAA,EAChB,GAAG,SAAA;IAEH,IAAM,gBAAgB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAE7D,IAAM,MAAM,GAAG,MAAM,CAA6C,IAAI,CAAC,CAAC;IAExE,IAAM,aAAa,GAAG,MAAM,CAA4B,IAAI,CAAC,CAAC;IAE9D,IAAM,8BAA8B,GAAG,MAAM,CAAiB,IAAI,CAAC,CAAC;IAEpE,IAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IAEtC,IAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAExC,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrC,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC;IAE/B,IAAM,KAAK,GAAG,OAAO,CACnB,cAAM,OAAA,CAAC;QACL,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;KACpB,CAAC,EAHI,CAGJ,EACF,CAAC,UAAU,EAAE,WAAW,CAAC,CAC1B,CAAC;IAEF,IAAM,qBAAqB,GAAG;QAC5B,IAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,YAAY,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAM,oBAAoB,GAAG;QAC3B,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAEvC,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,UAAC,KAAK;;YACnE,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC;gBAC5C,MAAA,WAAW,CAAC,OAAO,4DAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAM,uBAAuB,GAAG;QAC9B,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAM,UAAU,GAAG;QACjB,IAAM,UAAU,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;QAEzD,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC7B,oCAAoC;YACpC,IAAM,YAAY,yBAAQ,OAAO,GAAK,qBAAqB,EAAE,CAAE,CAAC;YAChE,IAAM,QAAQ,GAAG,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAG,MAAM,CAAC,CAAC;YAC/B,IAAI,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE,CAAC;gBACV,8CAA8C;gBAC9C,kEAAkE;gBAClE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC3B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CACnC,gBAAgB,CAAC,OAAO,+BAEtB,KAAK,OAAA,IACF,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,oBAAoB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GACtD,YAAY,GACZ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAE7B,gBAAgB,CACjB,CAAC;YACF,mCAAmC;YACnC,oBAAoB,EAAE,CAAC;QACzB,CAAC;IACH,CAAC,CAAC;IAEF,uDAAuD;IACvD,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAE1B,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,KAAK,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxC,8BAA8B,CAAC,OAAO,GAAG,IAAI,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC9B,qFAAqF;YACrF,KAAK,CAAC,kBAAkB,CACtB,EAAE,EACF;gBACE;oBACE,KAAK,EAAE,KAAK,CAAC,iBAAiB,EAAE;oBAChC,IAAI,EAAE,KAAK;iBACZ;aACF,EACD,SAAS,CACV,CAAC;YACF,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC9B,8BAA8B,CAAC,OAAO,GAAG,KAAK,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,gFAAgF;YAChF,4EAA4E;YACpE,IAAO,MAAM,GAA6B,OAAO,MAApC,EAAK,mBAAmB,UAAK,OAAO,EAAnD,SAAyC,CAAF,CAAa;YAC1D,MAAM,CAAC,OAAO,CAAC,aAAa,uBACvB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,oBAAoB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GACtD,mBAAmB,EACtB,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAEzB,SAAS,CAAC;QACR,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAEpB,SAAS,CAAC;QACR,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,SAAS,CACP,cAAM,OAAA;QACJ,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,uBAAuB,EAAE,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1B,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;IACH,CAAC,EARK,CAQL;IACD,uDAAuD;IACvD,EAAE,CACH,CAAC;IAEF,OAAO,CACL,6BACE,GAAG,EAAE,gBAAgB,EACrB,KAAK,EAAE,KAAK,EACZ,SAAS,EAAC,+BAA+B,GACzC,CACH,CAAC;AACJ,CAAC;AAED,YAAY,CAAC,WAAW,GAAG,cAAc,CAAC;AAE1C,eAAe,YAAY,CAAC"}