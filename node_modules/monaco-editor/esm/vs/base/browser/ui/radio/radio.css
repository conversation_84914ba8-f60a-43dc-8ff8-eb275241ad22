/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-custom-radio {
	display: flex;
}

.monaco-custom-radio > .monaco-button {
	border-radius: 0;
	font-size: 0.9em;
	line-height: 1em;
	padding-left: 0.5em;
	padding-right: 0.5em;
}

.monaco-custom-radio > .monaco-button:first-child {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
}

.monaco-custom-radio > .monaco-button:last-child {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

.monaco-custom-radio > .monaco-button:not(.active):not(:last-child) {
	border-right: none;
}

.monaco-custom-radio > .monaco-button.previous-active {
	border-left: none;
}

/* default color styles - based on CSS variables */

.monaco-custom-radio > .monaco-button {
	color: var(--vscode-radio-inactiveForeground);
	background-color: var(--vscode-radio-inactiveBackground);
	border-color: var(--vscode-radio-inactiveBorder, transparent);
}

.monaco-custom-radio > .monaco-button.active:hover,
.monaco-custom-radio > .monaco-button.active {
	color: var(--vscode-radio-activeForeground);
	background-color: var(--vscode-radio-activeBackground);
	border-color: var(--vscode-radio-activeBorder, transparent);
}

.hc-black .monaco-custom-radio > .monaco-button.active,
.hc-light .monaco-custom-radio > .monaco-button.active {
	border-color: var(--vscode-radio-activeBorder, transparent);
}

.hc-black .monaco-custom-radio > .monaco-button:not(.active),
.hc-light .monaco-custom-radio > .monaco-button:not(.active) {
	border-color: var(--vscode-radio-inactiveBorder, transparent);
}

.hc-black .monaco-custom-radio > .monaco-button:not(.active):hover,
.hc-light .monaco-custom-radio > .monaco-button:not(.active):hover {
	outline: 1px dashed var(--vscode-toolbar-hoverOutline);
	outline-offset: -1px
}

.monaco-custom-radio > .monaco-button:hover:not(.active) {
	background-color: var(--vscode-radio-inactiveHoverBackground);
}
