/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-breadcrumbs {
	user-select: none;
	-webkit-user-select: none;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	outline-style: none;
}

.monaco-breadcrumbs .monaco-breadcrumb-item {
	display: flex;
	align-items: center;
	flex: 0 1 auto;
	white-space: nowrap;
	cursor: pointer;
	align-self: center;
	height: 100%;
	outline: none;
}
.monaco-breadcrumbs.disabled .monaco-breadcrumb-item {
	cursor: default;
}

.monaco-breadcrumbs .monaco-breadcrumb-item .codicon-breadcrumb-separator {
	color: inherit;
}

.monaco-breadcrumbs .monaco-breadcrumb-item:first-of-type::before {
	content: ' ';
}
